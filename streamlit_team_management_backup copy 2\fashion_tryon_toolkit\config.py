#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时尚换装工具包 - 配置文件
Fashion Try-On Toolkit - Configuration
"""

# =============================================================================
# API 配置 (API Configuration)
# =============================================================================

# 302.AI API密钥 - 请替换为您的实际API密钥
# 302.AI API Key - Please replace with your actual API key
API_KEY = "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o"

# API基础URL
# API Base URL
BASE_URL = "https://api.302.ai"

# =============================================================================
# 处理参数 (Processing Parameters)
# =============================================================================

# 换装参数
# Fashion Try-On Parameters
FASHION_TRYON_CONFIG = {
    "modelImgSegLabels": "10",    # 10-上衣, 5-裤子, 6-裙子
    "clothesImgSegLabels": "10"   # 10-上衣, 5-裤子, 6-裙子
}

# 超时设置 (秒)
# Timeout Settings (seconds)
TIMEOUT_CONFIG = {
    "request_timeout": 30,        # 单次请求超时
    "task_max_wait": 600,         # 任务最大等待时间 (10分钟)
    "task_check_interval": 30,    # 任务状态检查间隔
    "max_retry_attempts": 20      # 最大重试次数
}

# =============================================================================
# 文件路径配置 (File Path Configuration)
# =============================================================================

# 输出目录
# Output Directories
OUTPUT_DIRS = {
    "temp": "temp_files",           # 临时文件目录
    "results": "results",           # 最终结果目录
    "batch_results": "batch_results", # 批量处理结果目录
    "analysis": "analysis_reports"   # 分析报告目录
}

# 支持的图片格式
# Supported Image Formats
SUPPORTED_FORMATS = [".jpg", ".jpeg", ".png", ".bmp", ".tiff"]

# =============================================================================
# 成本配置 (Cost Configuration)
# =============================================================================

# API成本 (PTC)
# API Costs (PTC)
API_COSTS = {
    "fashion_tryon": 0.1,      # 302.AI-ComfyUI 换装
    "remove_background": 0.5,   # Clipdrop 背景移除
    "white_background": 0.0     # 本地PIL白底合成 (免费)
}

# PTC到人民币汇率 (大约)
# PTC to CNY Exchange Rate (Approximate)
PTC_TO_CNY = 7.0

# =============================================================================
# 图片处理配置 (Image Processing Configuration)
# =============================================================================

# 白底背景配置
# White Background Configuration
WHITE_BACKGROUND_CONFIG = {
    "default_width": 512,
    "default_height": 640,
    "background_color": "white",
    "output_format": "PNG"
}

# 图片质量设置
# Image Quality Settings
IMAGE_QUALITY = {
    "jpeg_quality": 95,
    "png_compress_level": 6,
    "dpi": 300
}

# =============================================================================
# 日志配置 (Logging Configuration)
# =============================================================================

# 日志级别
# Log Levels
LOG_CONFIG = {
    "level": "INFO",           # DEBUG, INFO, WARNING, ERROR
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    "file_enabled": True,
    "console_enabled": True
}

# =============================================================================
# 批量处理配置 (Batch Processing Configuration)
# =============================================================================

# 批量处理设置
# Batch Processing Settings
BATCH_CONFIG = {
    "max_concurrent": 1,        # 最大并发数 (建议为1避免API限制)
    "progress_report_interval": 1, # 进度报告间隔
    "auto_retry_failed": True,  # 自动重试失败的任务
    "save_intermediate": True   # 保存中间结果
}

# =============================================================================
# 验证函数 (Validation Functions)
# =============================================================================

def validate_config():
    """验证配置是否正确"""
    errors = []
    
    # 检查API密钥
    if not API_KEY or API_KEY == "your-api-key-here":
        errors.append("请设置正确的API_KEY")
    
    # 检查必要的配置
    if not BASE_URL:
        errors.append("BASE_URL不能为空")
    
    if errors:
        raise ValueError("配置错误:\n" + "\n".join(errors))
    
    return True

def get_total_cost_per_image():
    """获取单张图片的总成本"""
    return sum(API_COSTS.values())

def get_total_cost_cny_per_image():
    """获取单张图片的人民币成本"""
    return get_total_cost_per_image() * PTC_TO_CNY

# =============================================================================
# 使用示例 (Usage Examples)
# =============================================================================

if __name__ == "__main__":
    # 验证配置
    try:
        validate_config()
        print("✅ 配置验证通过")
        print(f"💰 单张图片成本: {get_total_cost_per_image()} PTC (约{get_total_cost_cny_per_image():.1f}元)")
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
