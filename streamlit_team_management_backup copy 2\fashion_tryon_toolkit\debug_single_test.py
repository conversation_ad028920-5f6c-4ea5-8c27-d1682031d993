#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试单张照片处理 - 增强版调试信息
Debug Single Photo Processing - Enhanced Debug Info
"""

import requests
import time
import os
import json
from pathlib import Path
from PIL import Image
from config import *

def debug_image_info(image_path):
    """调试图片信息"""
    print(f"\n🔍 图片调试信息: {Path(image_path).name}")
    print("-" * 50)
    
    try:
        # 文件基本信息
        file_stat = os.stat(image_path)
        print(f"📁 文件大小: {file_stat.st_size / 1024:.1f} KB")
        print(f"📅 修改时间: {time.ctime(file_stat.st_mtime)}")
        
        # 图片信息
        with Image.open(image_path) as img:
            print(f"📏 图片尺寸: {img.size[0]}x{img.size[1]}")
            print(f"🎨 图片模式: {img.mode}")
            print(f"📋 图片格式: {img.format}")
            
            # 检查图片是否损坏
            img.verify()
            print("✅ 图片完整性: 正常")
            
    except Exception as e:
        print(f"❌ 图片检查失败: {str(e)}")
        return False
    
    return True

def enhanced_step1_fashion_tryon(model_image, clothes_image, photo_name):
    """增强版步骤1: 302.AI-ComfyUI 换装 - 带详细调试"""
    print(f"\n🎯 [调试版] 步骤1: 302.AI-ComfyUI 换装")
    print("=" * 60)
    
    # 调试图片信息
    print("📸 模特图片调试:")
    if not debug_image_info(model_image):
        return None
        
    print("\n👕 服装图片调试:")
    if not debug_image_info(clothes_image):
        return None
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/create-task"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "User-Agent": "Fashion-Tryon-Toolkit/1.0"
    }
    
    print(f"\n🌐 API调试信息:")
    print(f"📡 请求URL: {url}")
    print(f"🔑 API密钥: {API_KEY[:20]}...")
    
    try:
        with open(model_image, 'rb') as model_file, open(clothes_image, 'rb') as clothes_file:
            files = {
                'modelImageFile': (os.path.basename(model_image), model_file, 'image/jpeg'),
                'clothesImageFile': (os.path.basename(clothes_image), clothes_file, 'image/png')
            }
            
            data = FASHION_TRYON_CONFIG.copy()
            
            print(f"📤 发送请求参数:")
            print(f"   modelImgSegLabels: {data['modelImgSegLabels']}")
            print(f"   clothesImgSegLabels: {data['clothesImgSegLabels']}")
            
            print(f"📤 发送换装任务请求...")
            response = requests.post(
                url, 
                headers=headers, 
                files=files, 
                data=data, 
                timeout=TIMEOUT_CONFIG["request_timeout"]
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📋 响应头: {dict(response.headers)}")
            
            if response.status_code == 201:
                response_data = response.json()
                print(f"📄 响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 尝试多种方式获取taskId
                task_id = response_data.get('taskId')
                if not task_id and 'data' in response_data:
                    task_id = response_data['data'].get('taskId')

                if task_id:
                    print(f"✅ 换装任务创建成功！任务ID: {task_id}")
                    return enhanced_wait_for_fashion_task(task_id, photo_name)
                else:
                    print(f"❌ 响应中未找到taskId")
                    print(f"📄 完整响应结构: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                    return None
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                print(f"📄 错误响应: {response.text}")
                return None
                
    except requests.exceptions.Timeout:
        print(f"❌ 请求超时 (>{TIMEOUT_CONFIG['request_timeout']}秒)")
        return None
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {str(e)}")
        return None
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
        return None

def enhanced_wait_for_fashion_task(task_id, photo_name):
    """增强版等待任务完成 - 带详细调试"""
    print(f"⏳ [调试版] 等待换装任务完成...")
    print(f"🆔 任务ID: {task_id}")
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/query-task"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "User-Agent": "Fashion-Tryon-Toolkit/1.0"
    }
    
    max_attempts = TIMEOUT_CONFIG["max_retry_attempts"]
    check_interval = TIMEOUT_CONFIG["task_check_interval"]
    
    for attempt in range(1, max_attempts + 1):
        try:
            print(f"🔄 第{attempt}次查询... (最大{max_attempts}次)")
            
            response = requests.get(
                url,
                headers=headers,
                params={"taskId": task_id},
                timeout=TIMEOUT_CONFIG["request_timeout"]
            )
            
            print(f"📊 查询响应状态: {response.status_code}")
            
            if response.status_code == 200:
                task_data = response.json()
                status = task_data.get('status', 'UNKNOWN')
                print(f"📈 任务状态: {status}")
                
                if status == 'SUCCESS':
                    result_url = task_data.get('resultUrl')
                    if result_url:
                        print(f"🎉 换装任务完成！")
                        print(f"🔗 结果图URL: {result_url}")
                        
                        # 下载结果图片
                        output_filename = f"{photo_name}_step1_fashion.png"
                        output_path = os.path.join(OUTPUT_DIRS["temp"], output_filename)
                        
                        if download_image_with_debug(result_url, output_path):
                            return output_path
                        else:
                            return None
                    else:
                        print(f"❌ 成功状态但未找到结果URL")
                        print(f"📄 完整响应: {json.dumps(task_data, indent=2, ensure_ascii=False)}")
                        return None
                        
                elif status == 'FAILED':
                    print(f"❌ 任务失败，状态: {status}")
                    print(f"📄 失败详情: {json.dumps(task_data, indent=2, ensure_ascii=False)}")
                    return None
                    
                elif status in ['SUBMITTING', 'RUNNING']:
                    print(f"⏳ 任务进行中... 等待{check_interval}秒后再次查询...")
                    time.sleep(check_interval)
                    continue
                else:
                    print(f"❓ 未知状态: {status}")
                    print(f"📄 响应详情: {json.dumps(task_data, indent=2, ensure_ascii=False)}")
                    time.sleep(check_interval)
                    continue
                    
            else:
                print(f"❌ 查询失败: {response.status_code}")
                print(f"📄 错误响应: {response.text}")
                time.sleep(check_interval)
                continue
                
        except requests.exceptions.Timeout:
            print(f"❌ 网络请求超时 (>{TIMEOUT_CONFIG['request_timeout']}秒)")
            time.sleep(check_interval)
            continue
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 网络连接错误: {str(e)}")
            time.sleep(check_interval)
            continue
        except Exception as e:
            print(f"❌ 查询异常: {str(e)}")
            time.sleep(check_interval)
            continue
    
    print(f"❌ 任务超时，已尝试{max_attempts}次")
    return None

def download_image_with_debug(url, output_path):
    """带调试信息的图片下载"""
    print(f"📥 下载图片: {Path(output_path).name}")
    print(f"🔗 下载URL: {url}")
    
    try:
        response = requests.get(url, timeout=30)
        print(f"📊 下载响应状态: {response.status_code}")
        print(f"📏 文件大小: {len(response.content) / 1024:.1f} KB")
        
        if response.status_code == 200:
            with open(output_path, 'wb') as f:
                f.write(response.content)
            print(f"✅ 图片已保存: {output_path}")
            return True
        else:
            print(f"❌ 下载失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 下载异常: {str(e)}")
        return False

def test_single_photo(photo_path, clothes_image):
    """测试单张照片的详细处理"""
    photo_name = Path(photo_path).stem
    
    print("=" * 80)
    print(f"🧪 调试测试: {photo_name}")
    print("=" * 80)
    
    # 创建输出目录
    for dir_name in OUTPUT_DIRS.values():
        os.makedirs(dir_name, exist_ok=True)
    
    # 只测试步骤1（换装）
    result = enhanced_step1_fashion_tryon(photo_path, clothes_image, photo_name)
    
    if result:
        print(f"✅ 步骤1成功: {result}")
    else:
        print(f"❌ 步骤1失败")
    
    return result is not None

def main():
    """主函数 - 测试失败的照片"""
    # 服装模板
    clothes_image = "1111/微信图片_20250816210622_26_328.jpg"
    
    # 失败的照片列表
    failed_photos = [
        "1111/微信图片_2025-08-19_095051_534.jpg",
        "1111/微信图片_2025-08-19_095124_909.jpg", 
        "1111/微信图片_2025-08-19_095132_429.jpg"
    ]
    
    print("🧪 开始调试失败的照片")
    print("=" * 80)
    
    for i, photo in enumerate(failed_photos, 1):
        print(f"\n🔍 测试 {i}/{len(failed_photos)}: {Path(photo).name}")
        
        if not os.path.exists(photo):
            print(f"❌ 文件不存在: {photo}")
            continue
            
        success = test_single_photo(photo, clothes_image)
        
        if success:
            print(f"✅ {Path(photo).name} 测试成功")
        else:
            print(f"❌ {Path(photo).name} 测试失败")
        
        # 等待一下再测试下一张
        if i < len(failed_photos):
            print("\n⏳ 等待10秒后测试下一张...")
            time.sleep(10)

if __name__ == "__main__":
    main()
